import asyncio
import uvicorn
from fastapi import FastAPI
from contextlib import asynccontextmanager
from pydantic import BaseModel
from telegram import Update
from telegram.ext import Application
from config import settings
from src.core import load_all_handlers, get_handlers_info, register_handlers_to_application, scheduler, lottery_manager
from src.core.logger import default_logger as logger
import httpx

# Telegram API 基础URL
TELEGRAM_API = f"https://api.telegram.org/bot{settings.TELEGRAM_BOT_TOKEN}"

# 全局 telegram application 实例
telegram_app: Application | None = None

class TelegramUpdate(BaseModel):
    """Telegram更新对象模型"""
    update_id: int
    message: dict | None = None
    callback_query: dict | None = None


# 处理器统计信息
def get_handlers_stats():
    """获取处理器统计信息"""
    try:
        handlers_info = get_handlers_info()
        stats = {
            "total_handlers": len(handlers_info),
            "command_handlers": len([h for h in handlers_info if h["type"] == "CommandHandler"]),
            "message_handlers": len([h for h in handlers_info if h["type"] == "MessageHandler"]),
            "callback_handlers": len([h for h in handlers_info if h["type"] == "CallbackQueryHandler"]),
            "handlers_detail": handlers_info
        }
        return stats
    except Exception as e:
        logger.error(f"获取处理器统计信息时出错: {e}")
        return {"error": str(e)}


# 初始化 telegram application
async def init_telegram_app():
    """初始化 Telegram Application"""
    global telegram_app
    try:
        logger.info("正在初始化 Telegram Application...")

        # 加载所有处理器模块
        load_all_handlers()

        # 创建 Application 实例
        telegram_app = Application.builder().token(settings.TELEGRAM_BOT_TOKEN).build()

        # 自动注册所有处理器
        register_handlers_to_application(telegram_app)

        # 初始化 application
        await telegram_app.initialize()
        await telegram_app.start()

        # 初始化抽奖管理器
        lottery_manager.set_bot(telegram_app.bot)

        # 启动任务调度器
        await scheduler.start()

        # 启动定期检查任务
        await start_periodic_tasks()

        # 输出处理器统计信息
        stats = get_handlers_stats()
        logger.info(f"Telegram Application 初始化完成，共加载 {stats['total_handlers']} 个处理器")
        logger.info(f"处理器分布: 命令({stats['command_handlers']}) | 消息({stats['message_handlers']}) | 回调({stats['callback_handlers']})")

    except Exception as e:
        logger.error(f"初始化 Telegram Application 时出错: {e}")
        raise


# 设置Webhook
@asynccontextmanager
async def lifespan(app: FastAPI):
    """在应用启动时设置Telegram Webhook并初始化telegram app"""
    try:
        # 初始化 telegram application
        await init_telegram_app()

        # 设置 webhook
        webhook_url = f"{settings.WEBHOOK_BASE_URL}{settings.WEBHOOK_PATH}"
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{TELEGRAM_API}/setWebhook?url={webhook_url}")
            result = response.json()
            if result["ok"]:
                logger.info(f"Webhook 设置成功: {webhook_url}")
            else:
                logger.error(f"Webhook 设置失败: {result['description']}")

        yield

        # 清理资源
        if telegram_app:
            # 停止调度器
            await scheduler.stop()

            await telegram_app.stop()
            await telegram_app.shutdown()
            logger.info("Telegram Application 已关闭")

    except Exception as e:
        logger.error(f"应用生命周期管理出错: {e}")


async def start_periodic_tasks():
    """启动定期任务"""
    async def check_lottery_task():
        """定期检查抽奖任务"""
        while True:
            try:
                await lottery_manager.check_and_draw_ready_activities()
                await asyncio.sleep(60)  # 每分钟检查一次
            except Exception as e:
                logger.error(f"定期检查抽奖任务出错: {e}")
                await asyncio.sleep(60)

    # 启动定期检查任务
    asyncio.create_task(check_lottery_task())
    logger.info("定期任务已启动")


app = FastAPI(title="Telegram Bot API", lifespan=lifespan)


# 处理Webhook请求
@app.post("/webssse")
async def handle_webhook(update: TelegramUpdate):
    """处理Telegram发送的Webhook请求"""
    try:
        # 记录接收到的更新
        logger.info(f"收到更新: {update.update_id}")

        if telegram_app is None:
            logger.error("Telegram Application 未初始化")
            return {"ok": False, "error": "Telegram Application not initialized"}

        # 将 Pydantic 模型转换为 python-telegram-bot 的 Update 对象
        telegram_update = Update.de_json(update.model_dump(), telegram_app.bot)

        # 使用 telegram application 处理更新
        await telegram_app.process_update(telegram_update)

        return {"ok": True}

    except Exception as e:
        logger.error(f"处理Webhook时出错: {e}")
        return {"ok": False, "error": str(e)}


# 健康检查
@app.get("/health")
async def health_check():
    """健康检查端点"""
    logger.info("Health check endpoint was accessed.")

    # 检查 telegram app 状态
    telegram_status = "running" if telegram_app and telegram_app.running else "stopped"

    # 获取处理器统计信息
    handlers_stats = get_handlers_stats()

    return {
        "status": "ok",
        "telegram_app_status": telegram_status,
        "message": "服务运行正常",
        "handlers_stats": handlers_stats
    }


# 处理器信息端点
@app.get("/handlers")
async def get_handlers():
    """获取所有处理器信息"""
    logger.info("Handlers info endpoint was accessed.")

    try:
        stats = get_handlers_stats()
        return {
            "status": "success",
            "data": stats
        }
    except Exception as e:
        logger.error(f"获取处理器信息时出错: {e}")
        return {
            "status": "error",
            "message": str(e)
        }


# 异步启动服务
async def run_server():
    """启动 FastAPI 服务器"""
    try:
        logger.info("Starting the server...")
        config = uvicorn.Config(
            app=app,
            host=settings.HOST,
            port=settings.PORT,
            workers=settings.WORKERS,
            log_level=settings.LOG_LEVEL.lower()
        )
        server = uvicorn.Server(config)
        await server.serve()
    except Exception as e:
        logger.error(f"An error occurred while starting the server: {e}")


if __name__ == "__main__":
    asyncio.run(run_server())