"""
抽奖管理器

处理抽奖逻辑和状态管理
"""

import random
import uuid
from datetime import datetime, timedelta
from typing import List, Optional, Tuple
from telegram import Bo<PERSON>
from telegram.error import TelegramError

from src.models.lottery import LotteryActivity, LotteryParticipant, LotteryStatus
from src.config.lottery_config import lottery_config
from src.core.scheduler import scheduler
from src.core.logger import default_logger as logger


class LotteryManager:
    """抽奖管理器"""
    
    def __init__(self):
        self.bot: Optional[Bot] = None
    
    def set_bot(self, bot: Bot):
        """设置Bot实例"""
        self.bot = bot
    
    def generate_activity_id(self) -> str:
        """生成活动ID"""
        return f"lottery_{uuid.uuid4().hex[:8]}"
    
    def create_activity(self, creator_id: int, name: str, password: str,
                       draw_time: datetime, target_group_ids: List[int] = None,
                       target_group_names: List[str] = None, cover_photo: str = None,
                       description: str = None, winner_count: int = 1,
                       # 奖品相关参数
                       prizes: List = None, prize_name: str = "神秘奖品", prize_count: int = 1,
                       # 保持向后兼容
                       target_group_id: int = None, target_group_name: str = None) -> LotteryActivity:
        """创建抽奖活动"""
        activity_id = self.generate_activity_id()

        # 处理向后兼容性
        if target_group_ids is None:
            target_group_ids = []
            if target_group_id is not None:
                target_group_ids = [target_group_id]

        if target_group_names is None:
            target_group_names = []
            if target_group_name is not None:
                target_group_names = [target_group_name]

        # 处理奖品列表
        if prizes is None:
            # 向后兼容：创建单一奖品
            from src.models.lottery import Prize
            prizes = [Prize(name=prize_name, count=prize_count)]

        activity = LotteryActivity(
            id=activity_id,
            creator_id=creator_id,
            name=name,
            password=password,
            draw_time=draw_time,
            target_group_ids=target_group_ids,
            target_group_names=target_group_names,
            cover_photo=cover_photo,
            description=description,
            # 奖品相关字段
            prizes=prizes,
            prize_name=prize_name,
            prize_count=prize_count,
            winner_count=winner_count,
            status=LotteryStatus.WAITING
        )
        
        # 保存到配置
        if lottery_config.create_activity(activity):
            # 调度自动开奖任务
            self._schedule_auto_draw(activity)
            logger.info(f"创建抽奖活动成功: {activity_id} - {name}")
            return activity
        else:
            raise Exception("保存抽奖活动失败")
    
    def join_lottery(self, activity_id: str, user_id: int, username: str = None,
                    first_name: str = None, last_name: str = None) -> Tuple[bool, str]:
        """参与抽奖"""
        activity = lottery_config.get_activity(activity_id)
        if not activity:
            return False, "抽奖活动不存在"
        
        if not activity.can_participate():
            if activity.status == LotteryStatus.FINISHED:
                return False, "抽奖已结束"
            elif activity.status == LotteryStatus.CANCELLED:
                return False, "抽奖已取消"
            elif datetime.now() >= activity.draw_time:
                return False, "抽奖时间已过"
            else:
                return False, "当前无法参与抽奖"
        
        if activity.is_participant(user_id):
            return False, "您已经参与了这个抽奖"
        
        # 添加参与者
        success = activity.add_participant(user_id, username, first_name, last_name)
        if success:
            lottery_config.update_activity(activity)
            logger.info(f"用户 {user_id} 参与抽奖: {activity_id}")
            return True, "参与成功"
        else:
            return False, "参与失败"
    
    def find_activity_by_password(self, password: str, group_id: int) -> Optional[LotteryActivity]:
        """通过口令查找活动"""
        return lottery_config.find_activity_by_password(password, group_id)
    
    def draw_lottery(self, activity_id: str) -> Tuple[bool, str, List[LotteryParticipant]]:
        """执行抽奖"""
        activity = lottery_config.get_activity(activity_id)
        if not activity:
            return False, "抽奖活动不存在", []
        
        if activity.status != LotteryStatus.WAITING:
            return False, f"抽奖状态错误: {activity.status.value}", []
        
        if len(activity.participants) == 0:
            activity.status = LotteryStatus.FINISHED
            lottery_config.update_activity(activity)
            return False, "没有参与者", []
        
        # 设置状态为开奖中
        activity.status = LotteryStatus.DRAWING
        lottery_config.update_activity(activity)
        
        try:
            # 随机选择获奖者
            winner_count = min(activity.winner_count, len(activity.participants))
            winners = random.sample(activity.participants, winner_count)

            # 随机分配奖品给中奖者
            activity.assign_random_prizes(winners)

            activity.winners = winners
            activity.status = LotteryStatus.FINISHED
            lottery_config.update_activity(activity)

            logger.info(f"抽奖完成: {activity_id}, 获奖者: {[w.user_id for w in winners]}")
            return True, "抽奖完成", winners

        except Exception as e:
            # 抽奖失败，恢复状态
            activity.status = LotteryStatus.WAITING
            lottery_config.update_activity(activity)
            logger.error(f"抽奖失败: {activity_id}, 错误: {e}")
            return False, f"抽奖失败: {str(e)}", []

    def cancel_activity(self, activity_id: str, canceller_id: int) -> Tuple[bool, str]:
        """
        取消抽奖活动

        Args:
            activity_id: 活动ID
            canceller_id: 取消者用户ID

        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        activity = lottery_config.get_activity(activity_id)

        if not activity:
            return False, "抽奖活动不存在"

        # 检查权限：只有创建者可以取消
        if activity.creator_id != canceller_id:
            return False, "只有抽奖创建者可以取消抽奖"

        # 检查状态：只能取消等待开奖的活动
        if activity.status == LotteryStatus.FINISHED:
            return False, "抽奖已完成，无法取消"
        elif activity.status == LotteryStatus.CANCELLED:
            return False, "抽奖已被取消"
        elif activity.status == LotteryStatus.DRAWING:
            return False, "抽奖正在进行中，无法取消"

        # 取消活动
        activity.status = LotteryStatus.CANCELLED
        lottery_config.update_activity(activity)

        logger.info(f"抽奖活动已取消: {activity_id} by {canceller_id}")

        return True, f"抽奖活动 '{activity.name}' 已成功取消"

    def get_user_active_activities(self, user_id: int) -> List[LotteryActivity]:
        """
        获取用户创建的正在进行中的抽奖活动

        Args:
            user_id: 用户ID

        Returns:
            List[LotteryActivity]: 正在进行中的抽奖活动列表
        """
        activities = []
        for activity in lottery_config.activities.values():
            if (activity.creator_id == user_id and
                activity.status in [LotteryStatus.WAITING, LotteryStatus.CREATING]):
                activities.append(activity)

        # 按创建时间排序，最新的在前
        activities.sort(key=lambda x: x.create_time, reverse=True)
        return activities
    
    def cancel_activity(self, activity_id: str, canceller_id: int) -> Tuple[bool, str]:
        """
        取消抽奖活动

        Args:
            activity_id: 活动ID
            canceller_id: 取消者用户ID

        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        activity = lottery_config.get_activity(activity_id)

        if not activity:
            return False, "抽奖活动不存在"

        # 检查权限：只有创建者可以取消
        if activity.creator_id != canceller_id:
            return False, "只有抽奖创建者可以取消抽奖"

        # 检查状态：只能取消等待开奖的活动
        if activity.status == LotteryStatus.FINISHED:
            return False, "抽奖已完成，无法取消"
        elif activity.status == LotteryStatus.CANCELLED:
            return False, "抽奖已被取消"
        elif activity.status == LotteryStatus.DRAWING:
            return False, "抽奖正在进行中，无法取消"

        # 取消活动
        activity.status = LotteryStatus.CANCELLED
        lottery_config.update_activity(activity)

        logger.info(f"抽奖活动已取消: {activity_id} by {canceller_id}")

        return True, f"抽奖活动 '{activity.name}' 已成功取消"
    
    def get_user_activities(self, user_id: int) -> List[LotteryActivity]:
        """获取用户创建的活动"""
        return lottery_config.get_activities_by_creator(user_id)
    
    def get_group_activities(self, group_id: int) -> List[LotteryActivity]:
        """获取群组的活动"""
        return lottery_config.get_activities_by_group(group_id)
    
    def _schedule_auto_draw(self, activity: LotteryActivity):
        """调度自动开奖"""
        task_id = f"auto_draw_{activity.id}"
        
        scheduler.schedule_at(
            task_id=task_id,
            target_time=activity.draw_time,
            callback=self._auto_draw_callback,
            activity_id=activity.id
        )
        
        logger.info(f"调度自动开奖: {activity.id} 在 {activity.draw_time}")
    
    async def _auto_draw_callback(self, activity_id: str):
        """自动开奖回调"""
        try:
            logger.info(f"开始自动开奖: {activity_id}")
            
            success, message, winners = self.draw_lottery(activity_id)
            activity = lottery_config.get_activity(activity_id)
            
            if not activity or not self.bot:
                return
            
            # 发送开奖结果到所有目标群组
            if activity.target_group_ids:
                for group_id in activity.target_group_ids:
                    await self._send_draw_result_to_group(activity, success, message, winners, group_id)
            
            # 通知创建者
            await self._notify_creator(activity, success, message, winners)
            
        except Exception as e:
            logger.error(f"自动开奖回调出错: {activity_id}, 错误: {e}")
    
    async def _send_draw_result_to_group(self, activity: LotteryActivity, success: bool,
                                       message: str, winners: List[LotteryParticipant], group_id: int):
        """发送开奖结果到指定群组"""
        try:
            if success and winners:
                result_text = f"🎉 **抽奖结果公布**\n\n"
                result_text += f"🎯 活动名称: {activity.name}\n"

                # 显示奖品信息
                if activity.prizes and len(activity.prizes) > 1:
                    result_text += f"🎁 奖品 ({len(activity.prizes)}种):\n"
                    for prize in activity.prizes:
                        result_text += f"   • {prize.name} × {prize.count}\n"
                else:
                    result_text += f"🎁 奖品: {activity.prize_name} × {activity.prize_count}\n"

                result_text += f"👥 参与人数: {len(activity.participants)}\n"
                result_text += f"🏆 获奖人数: {len(winners)}\n\n"
                result_text += "🎊 **获奖者名单:**\n"

                for i, winner in enumerate(winners, 1):
                    prize_info = f" - {winner.won_prize}" if winner.won_prize else ""
                    result_text += f"{i}. {winner.get_display_name()}{prize_info}\n"

                result_text += f"\n恭喜以上获奖者！🎈"
            else:
                result_text = f"❌ **抽奖失败**\n\n"
                result_text += f"🎯 活动名称: {activity.name}\n"
                result_text += f"❗ 失败原因: {message}"

            # 调试日志：检查封面数据
            logger.info(f"自动开奖结果发送 - 活动: {activity.name}, 群组: {group_id}")
            logger.info(f"封面数据: {repr(activity.cover_photo)}")
            logger.info(f"封面判断: {bool(activity.cover_photo)}")

            # 发送开奖结果消息（支持封面显示）
            if activity.cover_photo:
                logger.info(f"使用 send_photo 发送自动开奖结果（带封面）到群组 {group_id}")
                result_msg = await self.bot.send_photo(
                    chat_id=group_id,
                    photo=activity.cover_photo,
                    caption=result_text,
                    parse_mode='Markdown'
                )
            else:
                logger.info(f"使用 send_message 发送自动开奖结果（无封面）到群组 {group_id}")
                result_msg = await self.bot.send_message(
                    chat_id=group_id,
                    text=result_text,
                    parse_mode='Markdown'
                )

            # 将开奖结果消息置顶
            try:
                await self.bot.pin_chat_message(
                    chat_id=group_id,
                    message_id=result_msg.message_id,
                    disable_notification=False  # 发送通知
                )
                logger.info(f"自动开奖结果消息已置顶: {result_msg.message_id} (群组: {group_id})")
            except Exception as pin_error:
                logger.warning(f"置顶自动开奖结果消息失败 (群组: {group_id}): {pin_error}")

        except TelegramError as e:
            logger.error(f"发送开奖结果到群组失败: {e}")
    
    async def _notify_creator(self, activity: LotteryActivity, success: bool,
                            message: str, winners: List[LotteryParticipant]):
        """通知创建者"""
        try:
            if success and winners:
                notification = f"🎉 您的抽奖活动 **{activity.name}** 已完成开奖！\n\n"

                # 显示奖品信息
                if activity.prizes and len(activity.prizes) > 1:
                    notification += f"🎁 奖品 ({len(activity.prizes)}种):\n"
                    for prize in activity.prizes:
                        notification += f"   • {prize.name} × {prize.count}\n"
                else:
                    notification += f"🎁 奖品: {activity.prize_name} × {activity.prize_count}\n"

                notification += f"👥 参与人数: {len(activity.participants)}\n"
                notification += f"🏆 获奖者:\n"

                for i, winner in enumerate(winners, 1):
                    prize_info = f" - {winner.won_prize}" if winner.won_prize else ""
                    notification += f"{i}. {winner.get_display_name()}{prize_info}\n"
            else:
                notification = f"❌ 您的抽奖活动 **{activity.name}** 开奖失败\n\n"
                notification += f"❗ 原因: {message}"

            # 私聊通知创建者也显示封面
            if activity.cover_photo:
                await self.bot.send_photo(
                    chat_id=activity.creator_id,
                    photo=activity.cover_photo,
                    caption=notification,
                    parse_mode='Markdown'
                )
            else:
                await self.bot.send_message(
                    chat_id=activity.creator_id,
                    text=notification,
                    parse_mode='Markdown'
                )

        except TelegramError as e:
            logger.error(f"通知创建者失败: {e}")
    
    async def check_and_draw_ready_activities(self):
        """检查并开奖准备就绪的活动"""
        ready_activities = lottery_config.get_ready_to_draw_activities()
        
        for activity in ready_activities:
            await self._auto_draw_callback(activity.id)


# 全局抽奖管理器实例
lottery_manager = LotteryManager()
